package com.stardust.autojs.engine;

import android.annotation.SuppressLint;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.webkit.JavascriptInterface;
import android.webkit.WebView;
import android.widget.Toast;

import com.bm.atool.App;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

public class ScriptEngine {
    private static final String TAG = "ScriptEngine";
    private WebView webView;
    private boolean isRunning = false;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private Map<String, Object> params = new HashMap<>();
    private StringBuilder consoleOutput = new StringBuilder();
    private CountDownLatch webViewCreatedLatch = new CountDownLatch(1);
    
    public ScriptEngine() {
        createWebView();
    }
    
    @SuppressLint("SetJavaScriptEnabled")
    private void createWebView() {
        mainHandler.post(() -> {
            try {
                webView = new WebView(App.getContext());
                webView.getSettings().setJavaScriptEnabled(true);
                webView.getSettings().setDomStorageEnabled(true);
                webView.addJavascriptInterface(new ScriptInterface(), "android");
                Log.d(TAG, "WebView创建成功");
                webViewCreatedLatch.countDown(); // 通知WebView创建完成
            } catch (Exception e) {
                Log.e(TAG, "创建WebView失败: " + e.getMessage(), e);
                webViewCreatedLatch.countDown(); // 即使失败也要countDown，避免无限等待
            }
        });
    }
    
    public void setParams(Map<String, Object> params) {
        if (params != null) {
            this.params = params;
        }
    }
    
    public void execute(String script) {
        Log.d(TAG, "开始执行脚本");
        // 等待WebView创建完成，最多等待5秒
        try {
            if (!webViewCreatedLatch.await(5, TimeUnit.SECONDS)) {
                Log.e(TAG, "等待WebView创建超时，无法执行脚本");
                return;
            }
        } catch (InterruptedException e) {
            Log.e(TAG, "等待WebView创建被中断", e);
            return;
        }
        
        if (webView == null) {
            Log.e(TAG, "WebView未初始化，无法执行脚本");
            return;
        }

        // 检查脚本内容
        if (script == null || script.trim().isEmpty()) {
            Log.e(TAG, "执行的脚本内容为空，无法执行");
            return;
        }

        Log.d(TAG, "准备执行脚本，长度: " + script.length() + " 字节");
        
        isRunning = true;
        
        // 构建完整脚本
        String fullScript = buildScript(script);
        Log.d(TAG, "构建的完整脚本长度: " + fullScript.length() + " 字节");
        
        // 主线程执行
        CountDownLatch latch = new CountDownLatch(1);
        mainHandler.post(() -> {
            try {
                Log.d(TAG, "开始执行脚本");
                Log.d(TAG, "完整脚本内容: " + fullScript); // 添加日志记录完整脚本内容
                webView.evaluateJavascript(fullScript, value -> {
                    Log.d(TAG, "脚本执行完成，结果: " + value);
                    latch.countDown();
                });
            } catch (Exception e) {
                Log.e(TAG, "脚本执行失败: " + e.getMessage(), e);
                latch.countDown();
            }
        });
        
        // 等待执行完成或超时
        try {
            if (latch.await(30000, java.util.concurrent.TimeUnit.MILLISECONDS)) {
                Log.d(TAG, "脚本执行完成");
            } else {
                Log.e(TAG, "脚本执行超时");
            }
        } catch (InterruptedException e) {
            Log.e(TAG, "脚本执行被中断", e);
        } finally {
            isRunning = false;
        }
    }
    
    public String getConsoleOutput() {
        return consoleOutput.toString();
    }
    
    public void forceStop() {
        isRunning = false;
        mainHandler.post(() -> {
            if (webView != null) {
                webView.evaluateJavascript("javascript:stopExecution=true;", null);
                Log.d(TAG, "已发送停止执行命令");
            }
        });
    }
    
    public void destroy() {
        isRunning = false;
        mainHandler.post(() -> {
            if (webView != null) {
                webView.destroy();
                webView = null;
            }
        });
    }
    
    private String buildScript(String userScript) {
        // 检查脚本是否为空
        if (userScript == null || userScript.trim().isEmpty()) {
            Log.e(TAG, "脚本内容为空");
            return "javascript:(function() { console.error('脚本内容为空'); })();";
        }

        // 构建参数传递
        StringBuilder paramsScript = new StringBuilder();
        paramsScript.append("const execArgv = {");
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            paramsScript.append("'").append(entry.getKey()).append("':");
            if (entry.getValue() instanceof String) {
                paramsScript.append("'").append(entry.getValue()).append("',");
            } else if (entry.getValue() instanceof Number || entry.getValue() instanceof Boolean) {
                paramsScript.append(entry.getValue()).append(",");
            } else {
                paramsScript.append("null,");
            }
        }
        paramsScript.append("};");
        
        // 转义用户脚本中的特殊字符
        String escapedUserScript = escapeScript(userScript);
        
        // 构建完整脚本
        return "javascript:(function() {" +
                "let stopExecution = false;" +
                
                // 重写console函数
                "console = {" +
                "  log: function(msg) { android.console('LOG', msg); }," +
                "  info: function(msg) { android.console('INFO', msg); }," +
                "  warn: function(msg) { android.console('WARN', msg); }," +
                "  error: function(msg) { android.console('ERROR', msg); }," +
                "};" +
                
                // 重写Toast函数
                "function toast(msg) { android.showToast(msg); };" +
                
                // 模拟auto.waitFor()
                "const auto = {" +
                "  waitFor: function() { console.log('请求无障碍权限'); return true; }" +
                "};" +
                
                // 设备信息
                "const device = {" +
                "  width: " + getScreenWidth() + "," +
                "  height: " + getScreenHeight() + "" +
                "};" +
                
                // 模拟点击函数
                "function click(x, y) {" +
                "  if(stopExecution) return false;" +
                "  console.log('点击: ' + x + ', ' + y);" +
                "  try {" +
                "    const result = android.click(x, y);" +
                "    console.log('点击结果: ' + result);" +
                "    return result;" +
                "  } catch (e) {" +
                "    console.error('点击异常: ' + e);" +
                "    return false;" +
                "  }" +
                "};" +
                
                // 滑动函数
                "function swipe(x1, y1, x2, y2, duration) {" +
                "  if(stopExecution) return false;" +
                "  duration = duration || 500; // 默认持续时间500ms" +
                "  console.log('滑动: (' + x1 + ', ' + y1 + ') -> (' + x2 + ', ' + y2 + '), 持续: ' + duration + 'ms');" +
                "  return android.swipe(x1, y1, x2, y2, duration);" +
                "};" +
                
                // 长按函数
                "function longClick(x, y, duration) {" +
                "  if(stopExecution) return false;" +
                "  duration = duration || 800; // 默认持续时间800ms" +
                "  console.log('长按: (' + x + ', ' + y + '), 持续: ' + duration + 'ms');" +
                "  return android.longClick(x, y, duration);" +
                "};" +
                
                // 睡眠函数
                "function sleep(ms) {" +
                "  if(stopExecution) return;" +
                "  const start = new Date().getTime();" +
                "  while(new Date().getTime() < start + ms) {" +
                "    if((new Date().getTime() - start) % 1000 === 0) {" +
                "      console.log('sleep: ' + (new Date().getTime() - start) + 'ms');" +
                "    }" +
                "    if(stopExecution) return;" +
                "  }" +
                "};" +
                
                // 参数
                paramsScript.toString() +
                
                // 用户脚本
                "try { eval(" + toJsonString(escapedUserScript) + "); } catch(e) { console.error('脚本执行错误: ' + e); }" +
                "})();";
    }
    
    /**
     * 转义脚本中的特殊字符，确保生成的JavaScript代码有效
     * @param script 原始脚本
     * @return 转义后的脚本
     */
    private String escapeScript(String script) {
        if (script == null || script.isEmpty()) {
            return "";
        }
        
        // 直接返回用户脚本，不使用eval包装
        // 这样可以避免语法错误，并且更安全
        return script;
    }
    
    /**
     * 将字符串转换为JSON格式的字符串，用于安全地嵌入到JavaScript代码中
     * @param str 原始字符串
     * @return JSON格式的字符串
     */
    private String toJsonString(String str) {
        if (str == null) {
            return "null";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("\"");
        
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            switch (c) {
                case '"':
                    sb.append("\\\"");
                    break;
                case '\\':
                    sb.append("\\\\");
                    break;
                case '\b':
                    sb.append("\\b");
                    break;
                case '\f':
                    sb.append("\\f");
                    break;
                case '\n':
                    sb.append("\\n");
                    break;
                case '\r':
                    sb.append("\\r");
                    break;
                case '\t':
                    sb.append("\\t");
                    break;
                default:
                    if (c < 0x20) {
                        // Unicode escape for control characters
                        sb.append(String.format("\\u%04x", (int) c));
                    } else {
                        sb.append(c);
                    }
                    break;
            }
        }
        
        sb.append("\"");
        return sb.toString();
    }
    
    private int getScreenWidth() {
        return App.getContext().getResources().getDisplayMetrics().widthPixels;
    }
    
    private int getScreenHeight() {
        return App.getContext().getResources().getDisplayMetrics().heightPixels;
    }
    
    class ScriptInterface {
        @JavascriptInterface
        public void console(String level, String message) {
            String logMessage = level + ": " + message;
            consoleOutput.append(logMessage).append("\n");
            Log.d(TAG, "JS Console: " + logMessage);
        }
        
        @JavascriptInterface
        public void showToast(final String message) {
            Log.d(TAG, "显示Toast: " + message);
            mainHandler.post(() -> 
                Toast.makeText(App.getContext(), message, Toast.LENGTH_SHORT).show()
            );
        }
        
        @JavascriptInterface
        public boolean click(final int x, final int y) {
            Log.d(TAG, "模拟点击坐标: (" + x + ", " + y + ")");
            
            // 检查无障碍服务是否可用
            if (!com.bm.atool.utils.AccessibilityUtil.isAccessibilityServiceEnabled()) {
                Log.e(TAG, "无障碍服务未启用，无法执行点击操作");
                mainHandler.post(() ->
                    Toast.makeText(App.getContext(),
                        "无障碍服务未启用，无法执行点击操作",
                        Toast.LENGTH_LONG).show()
                );
                return false;
            }
            
            // 使用AccessibilityUtil执行实际点击
            boolean clickResult = com.bm.atool.utils.AccessibilityUtil.click(x, y);
            Log.d(TAG, "点击操作结果: " + clickResult);
            
            // 无论点击是否成功，都显示Toast提示
            mainHandler.post(() ->
                Toast.makeText(App.getContext(),
                    clickResult ? "点击成功: (" + x + ", " + y + ")" : "点击失败: (" + x + ", " + y + ")",
                    Toast.LENGTH_SHORT).show()
            );
            
            return clickResult;
        }
        
        @JavascriptInterface
        public boolean swipe(final int startX, final int startY, final int endX, final int endY, final long duration) {
            Log.d(TAG, "模拟滑动: (" + startX + ", " + startY + ") -> (" + endX + ", " + endY + "), 持续: " + duration + "ms");
            
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                // 使用AccessibilityUtil执行实际滑动
                boolean swipeResult = com.bm.atool.utils.AccessibilityUtil.swipe(startX, startY, endX, endY, duration);
                
                // 显示操作结果Toast
                final boolean finalResult = swipeResult;
                mainHandler.post(() -> 
                    Toast.makeText(App.getContext(), 
                        finalResult ? "滑动成功" : "滑动失败", 
                        Toast.LENGTH_SHORT).show()
                );
                
                return swipeResult;
            } else {
                Log.e(TAG, "当前Android版本不支持手势API，无法执行滑动");
                mainHandler.post(() -> 
                    Toast.makeText(App.getContext(), 
                        "设备不支持滑动操作", 
                        Toast.LENGTH_SHORT).show()
                );
                return false;
            }
        }
        
        @JavascriptInterface
        public boolean longClick(final int x, final int y, final long duration) {
            Log.d(TAG, "模拟长按: (" + x + ", " + y + "), 持续: " + duration + "ms");
            
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                // 使用AccessibilityUtil执行实际长按
                boolean longClickResult = com.bm.atool.utils.AccessibilityUtil.longClick(x, y, duration);
                
                // 显示操作结果Toast
                final boolean finalResult = longClickResult;
                mainHandler.post(() -> 
                    Toast.makeText(App.getContext(), 
                        finalResult ? "长按成功" : "长按失败", 
                        Toast.LENGTH_SHORT).show()
                );
                
                return longClickResult;
            } else {
                Log.e(TAG, "当前Android版本不支持手势API，无法执行长按");
                mainHandler.post(() -> 
                    Toast.makeText(App.getContext(), 
                        "设备不支持长按操作", 
                        Toast.LENGTH_SHORT).show()
                );
                return false;
            }
        }
    }
}