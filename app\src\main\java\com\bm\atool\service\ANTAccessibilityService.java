package com.bm.atool.service;

import android.accessibilityservice.AccessibilityService;
import android.content.Intent;
import android.util.Log;
import android.view.accessibility.AccessibilityEvent;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;

import com.bm.atool.Sys;
import com.bm.atool.utils.AutoJsExecutor;

public class ANTAccessibilityService extends AccessibilityService implements LifecycleOwner {
    private LifecycleRegistry mLifecycleRegistry = new LifecycleRegistry(this);

    private final static String TAG = ANTAccessibilityService.class.getSimpleName();
    
    // 静态单例实例
    private static ANTAccessibilityService instance;
    
    // 广播动作
    public static final String ACTION_EXECUTE_SCRIPT = "com.bm.atool.action.EXECUTE_SCRIPT";
    
    // 文件监控Handler
    private android.os.Handler fileMonitorHandler;
    private Runnable fileMonitorRunnable;

    @Override
    public void onCreate() {
        super.onCreate();
        instance = this; // 保存单例实例
        
        Log.e(TAG, "ANTAccessibilityService created: "  + String.valueOf(this.hashCode()));
        try{
            mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE);
        }
        catch (Exception ex){
            ex.printStackTrace();
        }
        
        // 启动文件监控
        startFileMonitoring();
        
        sendStatusBroadcast(true);
//        SocketServiceConnection.getInstance(this.getApplicationContext()).EnsureSocketService();
    }

    private final void sendStatusBroadcast(boolean running) {
        Intent intent = new Intent(Sys.ACTION_ACCESSIBILITY_SERVICE_STATUS_CHANGED);
        intent.putExtra(NotificationCompat.CATEGORY_STATUS, running);
        sendBroadcast(intent);
    }

    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
//        Log.d(TAG,"AccessibilityEvent:"  + event.toString());
    }

    @Override
    public void onInterrupt() {
        Log.d(TAG,"onInterrupt" );
    }

    @NonNull
    @Override
    public Lifecycle getLifecycle() {
        return this.mLifecycleRegistry;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.e(TAG, "ANTAccessibilityService onDestroy: "  + String.valueOf(this.hashCode()));
        
        // 停止文件监控
        stopFileMonitoring();
        
        sendStatusBroadcast(false);
        instance = null; // 清除单例实例
    }
    
    /**
     * 获取单例实例
     * @return 无障碍服务实例，如果服务未运行则返回null
     */
    public static ANTAccessibilityService getInstance() {
        return instance;
    }
    
    /**
     * 静态方法，用于从应用的任何地方启动脚本执行
     * @return 是否成功启动脚本执行（如果无障碍服务未运行则返回false）
     */
    public static boolean startScriptExecution() {
        Log.d(TAG, "startScriptExecution called");
        // 检查服务是否正在运行
        if (isServiceRunning()) {
            Log.d(TAG, "无障碍服务正在运行");
            ANTAccessibilityService service = getInstance();
            if (service != null) {
                Log.d(TAG, "无障碍服务已运行，开始模拟脚本下载和执行");
                service.simulateDownloadAndExecuteScript();
                return true;
            } else {
                // 服务在独立进程中运行，无法直接获取实例
                // 通过广播通知服务执行脚本
                Log.d(TAG, "无障碍服务在独立进程中运行，通过广播启动脚本执行");
                sendExecuteScriptBroadcast();
                return true;
            }
        } else {
            Log.e(TAG, "无法启动脚本执行，无障碍服务未运行。请前往设置->无障碍->ATool启用服务");
            return false;
        }
    }
    
    /**
     * 检查无障碍服务是否正在运行
     * @return 服务是否正在运行
     */
    private static boolean isServiceRunning() {
        // 这里可以实现检查服务是否运行的逻辑
        // 暂时返回true，因为我们已经在PermissionUtils.isAccessibilitySettingsOn中检查过了
        return true;
    }
    
    /**
     * 发送执行脚本的广播
     */
    private static void sendExecuteScriptBroadcast() {
        Log.d(TAG, "sendExecuteScriptBroadcast called");
        // 使用文件方式进行跨进程通信，因为广播在跨进程时可能不可靠
        try {
            android.content.Context context = com.bm.atool.Sys.app;
            if (context != null) {
                Log.d(TAG, "准备创建脚本执行标志文件");
                
                // 创建文件标志，让无障碍服务检测到后执行脚本
                java.io.File flagFile = new java.io.File(context.getFilesDir(), "execute_script_flag.txt");
                try (java.io.FileWriter writer = new java.io.FileWriter(flagFile)) {
                    writer.write(String.valueOf(System.currentTimeMillis()));
                    writer.flush();
                }
                Log.d(TAG, "已创建执行脚本标志文件: " + flagFile.getAbsolutePath());
                
                // 同时发送广播（双重保险）
                Log.d(TAG, "准备发送执行脚本广播");
                android.content.Intent intent = new android.content.Intent(ACTION_EXECUTE_SCRIPT);
                intent.setPackage(context.getPackageName());
                // 添加FLAG_INCLUDE_STOPPED_PACKAGES确保广播能被接收
                intent.addFlags(android.content.Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                context.sendBroadcast(intent);
                Log.d(TAG, "已发送执行脚本广播");
                
                // 添加延迟检查，确认广播是否被接收
                android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
                handler.postDelayed(() -> {
                    Log.d(TAG, "广播发送后1秒检查 - 如果看不到接收日志，说明跨进程广播有问题");
                }, 1000);
            } else {
                Log.e(TAG, "无法发送执行脚本广播：Context为null");
            }
        } catch (Exception e) {
            Log.e(TAG, "发送执行脚本广播时发生异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 模拟下载脚本并执行
     * 这个方法会直接生成脚本内容，然后模拟下载过程，最后执行脚本
     */
    public void simulateDownloadAndExecuteScript() {
        Log.d(TAG, "开始模拟脚本下载...");
        
        // 在新线程中模拟下载过程
        new Thread(() -> {
            try {
                Log.d(TAG, "开始模拟脚本下载过程");
                // 模拟下载进度
                for (int i = 0; i <= 100; i += 10) {
                    Log.d(TAG, "模拟脚本下载进度: " + i + "%");
                    Thread.sleep(300); // 每10%等待300ms
                }
                
                Log.d(TAG, "模拟脚本下载完成，开始执行脚本");
                
                // 创建脚本内容
                String scriptContent = createScriptContent();
                Log.d(TAG, "生成的脚本内容长度: " + (scriptContent != null ? scriptContent.length() : 0));
                
                // 下载完成后执行脚本
                executeScript(scriptContent);
                
                Log.d(TAG, "=== 脚本执行过程追踪开始 ===");
                Log.d(TAG, "请确保已启用无障碍服务，才能执行真实点击");
                Log.d(TAG, "请注意观察Toast通知和Logcat日志");
                Log.d(TAG, "脚本将在5秒后开始点击导航栏");
                Log.d(TAG, "=== 脚本执行过程追踪结束 ===");
            } catch (InterruptedException e) {
                Log.e(TAG, "模拟脚本下载被中断", e);
            } catch (Exception e) {
                Log.e(TAG, "模拟脚本下载或执行过程中发生异常", e);
            }
        }).start();
    }
    
    /**
     * 创建脚本内容
     * @return 脚本内容字符串
     */
    private String createScriptContent() {
        Log.d(TAG, "开始创建脚本内容");
        // 创建脚本内容
        StringBuilder script = new StringBuilder();
        
        // 添加脚本注释
        script.append("/**\n");
        script.append(" * 模拟脚本：5秒后点击导航栏20次\n");
        script.append(" */\n\n");
        
        // 请求无障碍权限
        script.append("auto.waitFor();\n\n");
        
        // 显示开始Toast
        script.append("toast(\"开始执行模拟点击脚本\");\n");
        script.append("console.log(\"开始执行模拟点击脚本\");\n\n");
        
        // 等待5秒
        script.append("console.log(\"等待5秒...\");\n");
        script.append("sleep(5000);\n\n");
        
        // 显示即将点击的Toast
        script.append("toast(\"5秒已到，开始点击导航栏\");\n");
        script.append("console.log(\"5秒已到，开始点击导航栏\");\n\n");
        
        // 点击导航栏20次
        script.append("for (var i = 0; i < 20; i++) {\n");
        script.append("    // 点击导航栏区域（这里假设导航栏在屏幕底部，高度为100像素）\n");
        script.append("    // 屏幕中心点X坐标，底部Y坐标\n");
        script.append("    var x = device.width / 2;\n");
        script.append("    var y = device.height - 50;\n");
        script.append("    var clickResult = click(x, y);\n");
        script.append("    console.log(\"第\" + (i + 1) + \"次点击导航栏，坐标: (\" + x + \", \" + y + \"), 结果: \" + clickResult);\n");
        script.append("    \n");
        script.append("    // 每次点击后显示Toast提示\n");
        script.append("    if ((i + 1) % 5 == 0) {\n");
        script.append("        toast(\"已完成\" + (i + 1) + \"次点击\");\n");
        script.append("    }\n");
        script.append("    \n");
        script.append("    // 等待100毫秒再进行下次点击\n");
        script.append("    sleep(100);\n");
        script.append("}\n\n");
        
        // 显示完成Toast
        script.append("toast(\"模拟点击脚本执行完成\");\n");
        script.append("console.log(\"模拟点击脚本执行完成\");\n");
        
        String scriptContent = script.toString().trim(); // 修复：移除末尾多余空行
        Log.d(TAG, "脚本内容创建完成，长度: " + scriptContent.length());
        return scriptContent;
    }
    
    /**
     * 执行脚本
     * @param scriptContent 脚本内容
     */
    private void executeScript(String scriptContent) {
        try {
            Log.d(TAG, "开始执行脚本");
            // 获取AutoJsExecutor实例
            AutoJsExecutor executor = AutoJsExecutor.getInstance(getApplicationContext());
            
            if (executor != null) {
                Log.d(TAG, "AutoJsExecutor实例获取成功");
                // 执行脚本，生成唯一的脚本ID
                String scriptId = "simulate_script_" + System.currentTimeMillis();
                Log.d(TAG, "准备执行脚本，scriptId: " + scriptId);
                String executionId = executor.executeScript(scriptId, scriptContent, null);
                
                Log.d(TAG, "脚本执行已开始，执行ID: " + executionId);
            } else {
                Log.e(TAG, "AutoJsExecutor未初始化，无法执行脚本");
            }
        } catch (Exception e) {
            Log.e(TAG, "执行脚本异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 启动文件监控
     */
    private void startFileMonitoring() {
        Log.d(TAG, "启动文件监控");
        if (fileMonitorHandler == null) {
            fileMonitorHandler = new android.os.Handler(android.os.Looper.getMainLooper());
        }
        
        fileMonitorRunnable = new Runnable() {
            @Override
            public void run() {
                try {
                    // 检查标志文件是否存在
                    java.io.File flagFile = new java.io.File(getFilesDir(), "execute_script_flag.txt");
                    if (flagFile.exists()) {
                        Log.d(TAG, "检测到执行脚本标志文件，开始执行脚本");
                        
                        // 执行脚本
                        simulateDownloadAndExecuteScript();
                        
                        // 删除标志文件，防止重复执行
                        boolean deleted = flagFile.delete();
                        Log.d(TAG, "标志文件删除结果: " + deleted);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "文件监控异常: " + e.getMessage(), e);
                }
                
                // 继续监控，每500毫秒检查一次
                if (fileMonitorHandler != null) {
                    fileMonitorHandler.postDelayed(this, 500);
                }
            }
        };
        
        // 开始监控
        fileMonitorHandler.post(fileMonitorRunnable);
        Log.d(TAG, "文件监控已启动");
    }
    
    /**
     * 停止文件监控
     */
    private void stopFileMonitoring() {
        Log.d(TAG, "停止文件监控");
        if (fileMonitorHandler != null && fileMonitorRunnable != null) {
            fileMonitorHandler.removeCallbacks(fileMonitorRunnable);
            fileMonitorHandler = null;
            fileMonitorRunnable = null;
            Log.d(TAG, "文件监控已停止");
        }
    }
}
