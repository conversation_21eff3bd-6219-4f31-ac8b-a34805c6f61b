<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.stardust.autojs.engine.ScriptEngineTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-07-27T13:49:05" hostname="DESKTOP-FVE376B" time="0.008">
  <properties/>
  <testcase name="testJsonStringEscaping" classname="com.stardust.autojs.engine.ScriptEngineTest" time="0.002"/>
  <testcase name="testBuildScriptWithParams" classname="com.stardust.autojs.engine.ScriptEngineTest" time="0.004"/>
  <testcase name="testBuildScriptWithComplexUserScript" classname="com.stardust.autojs.engine.ScriptEngineTest" time="0.0"/>
  <testcase name="testBuildScriptWithEmptyParams" classname="com.stardust.autojs.engine.ScriptEngineTest" time="0.0"/>
  <system-out><![CDATA[Generated script: javascript:(function() {let stopExecution = false;console = {  log: function(msg) { android.console('LOG', msg); },  info: function(msg) { android.console('INFO', msg); },  warn: function(msg) { android.console('WARN', msg); },  error: function(msg) { android.console('ERROR', msg); }};function toast(msg) { android.showToast(msg); };const auto = {  waitFor: function() { console.log('请求无障碍权限'); return true; }};const device = {  width: 1440,  height: 3120};function click(x, y) {  if(stopExecution) return false;  console.log('点击: ' + x + ', ' + y);  try {    const result = android.click(x, y);    console.log('点击结果: ' + result);    return result;  } catch (e) {    console.error('点击异常: ' + e);    return false;  }};function swipe(x1, y1, x2, y2, duration) {  if(stopExecution) return false;  duration = duration || 500;  console.log('滑动: (' + x1 + ', ' + y1 + ') -> (' + x2 + ', ' + y2 + '), 持续: ' + duration + 'ms');  return android.swipe(x1, y1, x2, y2, duration);};function longClick(x, y, duration) {  if(stopExecution) return false;  duration = duration || 800;  console.log('长按: (' + x + ', ' + y + '), 持续: ' + duration + 'ms');  return android.longClick(x, y, duration);};function sleep(ms) {  if(stopExecution) return;  const start = new Date().getTime();  while(new Date().getTime() < start + ms) {    if((new Date().getTime() - start) % 1000 === 0) {      console.log('sleep: ' + (new Date().getTime() - start) + 'ms');    }    if(stopExecution) return;  }};const execArgv = {'testBoolean':true,'testNumber':42,'testString':'hello'};try { eval("console.log('Test with params');"); } catch(e) { console.error('脚本执行错误: ' + e); }})();
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
