<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class com.stardust.autojs.engine.ScriptEngineTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class com.stardust.autojs.engine.ScriptEngineTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.stardust.autojs.engine.html">com.stardust.autojs.engine</a> &gt; ScriptEngineTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">4</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.006s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
<li>
<a href="#tab1">Standard output</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testBuildScriptWithComplexUserScript</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testBuildScriptWithEmptyParams</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testBuildScriptWithParams</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testJsonStringEscaping</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div id="tab1" class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>Generated script: javascript:(function() {let stopExecution = false;console = {  log: function(msg) { android.console('LOG', msg); },  info: function(msg) { android.console('INFO', msg); },  warn: function(msg) { android.console('WARN', msg); },  error: function(msg) { android.console('ERROR', msg); }};function toast(msg) { android.showToast(msg); };const auto = {  waitFor: function() { console.log('请求无障碍权限'); return true; }};const device = {  width: 1440,  height: 3120};function click(x, y) {  if(stopExecution) return false;  console.log('点击: ' + x + ', ' + y);  try {    const result = android.click(x, y);    console.log('点击结果: ' + result);    return result;  } catch (e) {    console.error('点击异常: ' + e);    return false;  }};function swipe(x1, y1, x2, y2, duration) {  if(stopExecution) return false;  duration = duration || 500;  console.log('滑动: (' + x1 + ', ' + y1 + ') -&gt; (' + x2 + ', ' + y2 + '), 持续: ' + duration + 'ms');  return android.swipe(x1, y1, x2, y2, duration);};function longClick(x, y, duration) {  if(stopExecution) return false;  duration = duration || 800;  console.log('长按: (' + x + ', ' + y + '), 持续: ' + duration + 'ms');  return android.longClick(x, y, duration);};function sleep(ms) {  if(stopExecution) return;  const start = new Date().getTime();  while(new Date().getTime() &lt; start + ms) {    if((new Date().getTime() - start) % 1000 === 0) {      console.log('sleep: ' + (new Date().getTime() - start) + 'ms');    }    if(stopExecution) return;  }};const execArgv = {'testBoolean':true,'testNumber':42,'testString':'hello'};try { eval(&quot;console.log('Test with params');&quot;); } catch(e) { console.error('脚本执行错误: ' + e); }})();
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.7</a> at 2025年7月27日 下午9:49:05</p>
</div>
</div>
</body>
</html>
